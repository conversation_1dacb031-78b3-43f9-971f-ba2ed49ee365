import numpy as np
import nltk
import string

############################################################
# CIS 521: Homework 1
############################################################

student_name = "<PERSON>"

# This is where your grade report will be sent.
student_email = "<EMAIL>"

############################################################
# Section 1: Python Concepts
############################################################

python_concepts_question_1 = """
Python is strongly typed because variables have types which
are not converted in order to perform certain operations.
For example: if a = 3 and b = "3", then attempting to add a
and b will result in a TypeError, since a is an integer and
b is a string. Likewise, attempting to concatenate a string
and an integer will also result in a TypeError.

Python is dynamically typed because the type of a variable
can be changed while the program is running. For example:
if a = 3, then a can be changed to a = "3" later in the
program, in which case it would be changing from an integer
to a string.
"""

python_concepts_question_2 = """
You'll get a TypeError with the referenced code because
dictionary keys must be immutable. Since lists are mutable,
they cannot be used as keys in a dictionary. In order to fix
this, we need to use tuples to represent the 2-dimensional
points, since tuples are immutable.

points_to_names = {
    (0, 0): "home",
    (1, 2): "school",
    (-1, 1): "market"
}
"""

python_concepts_question_3 = """
concatenate2 will be significantly faster than concatenate1
for large inputs because concatenate1 uses the += operator
to concatenate strings, which will create a new string each
time it is called as opposed to concatenate2 which uses the
join() method to concatenate the strings in the list,
therefore only creating one new string.
"""


############################################################
# Section 2: Working with Lists
############################################################


def extract_and_apply(lst, p, f):
    return [f(x) for x in lst if p(x)]


def concatenate(seqs):
    return [element for iterable in seqs for element in iterable]


def transpose(matrix):
    rows = len(matrix)
    columns = len(matrix[0])
    transposed_matrix = []
    for column in range(columns):
        transposed_row_values = []
        for row in range(rows):
            transposed_row_values.append(matrix[row][column])
        transposed_matrix.append(transposed_row_values)
    return transposed_matrix


############################################################
# Section 3: Sequence Slicing
############################################################


def copy(seq):
    return seq[:]


def all_but_last(seq):
    return seq[:-1]


def every_other(seq):
    return seq[::2]


############################################################
# Section 4: Combinatorial Algorithms
############################################################


def prefixes(seq):
    n = len(seq)
    for x in range(n + 1):
        yield seq[:x]


def suffixes(seq):
    n = len(seq)
    for x in range(n + 1):
        yield seq[x:]


def slices(seq):
    n = len(seq)
    start_index = 0
    stop_index = 1
    while True:
        if start_index == n:
            break
        if start_index == stop_index:
            continue
        if stop_index == n + 1:
            start_index += 1
            stop_index = start_index + 1
        if seq[start_index: stop_index]:
            yield seq[start_index: stop_index]
        stop_index += 1


############################################################
# Section 5: Text Processing
############################################################


def normalize(text):
    word_list = []
    # Remove multiple spaces between words.
    for word in text.split():
        word_list.append(word.lower().strip())
    return " ".join(word_list).strip()


def no_vowels(text):
    vowels = ['a', 'e', 'i', 'o', 'u']
    for vowel in vowels:
        text = text.replace(vowel, '').replace(vowel.upper(), '')
    return text


def digits_to_words(text):
    digit_name_dict = {
        '0': 'zero',
        '1': 'one',
        '2': 'two',
        '3': 'three',
        '4': 'four',
        '5': 'five',
        '6': 'six',
        '7': 'seven',
        '8': 'eight',
        '9': 'nine',
    }
    digit_name_list = []
    for char in text:
        if char in digit_name_dict:
            digit_name_list.append(digit_name_dict.get(char))
    return ' '.join(digit_name_list) if digit_name_list else ''


def to_mixed_case(name):
    word_list = name.split('_')
    mixed_case_name = ''
    if not word_list:
        return mixed_case_name
    else:
        starting_index = 0
        for index in range(len(word_list)):
            if word_list[index]:
                mixed_case_name += word_list[index].lower()
                starting_index = index
                break
        else:
            return mixed_case_name
        if len(word_list) > starting_index:
            for word in word_list[starting_index + 1:]:
                mixed_case_name += word.capitalize()
    return mixed_case_name


############################################################
# Section 6: Polynomials
############################################################


class Polynomial(object):

    def __init__(self, polynomial):
        self.polynomial = tuple(polynomial)

    def get_polynomial(self):
        return self.polynomial

    def __neg__(self):
        return Polynomial([(-x, y) for (x, y) in self.polynomial])

    def __add__(self, other):
        poly_list = list(self.get_polynomial())
        poly_list.extend(other.get_polynomial())
        return Polynomial(poly_list)

    def __sub__(self, other):
        poly_list = list(self.get_polynomial())
        poly_list.extend([(-x, y) for (x, y) in other.polynomial])
        return Polynomial(poly_list)

    def __mul__(self, other):
        # Multiply each term in one polynomial by each term
        # in the other polynomial.
        poly_list = []
        for coef1, exp1 in self.get_polynomial():
            for coef2, exp2 in other.get_polynomial():
                poly_list.append((coef1 * coef2, exp1 + exp2))
        return Polynomial(poly_list)

    def __call__(self, x):
        value = 0
        for coef, exp in self.get_polynomial():
            value += coef * (x ** exp)
        return value

    def simplify(self):
        simplified_poly_list = list()
        exp_coef_dict = dict()
        for coef, exp in self.get_polynomial():
            if exp_coef_dict.get(exp):
                exp_coef_dict[exp] = exp_coef_dict.get(exp) + coef
            else:
                exp_coef_dict[exp] = coef
        for exp in sorted(exp_coef_dict.keys(), reverse=True):
            if exp_coef_dict.get(exp) != 0:
                simplified_poly_list.append((exp_coef_dict.get(exp), exp))
        if len(simplified_poly_list) == 0:
            simplified_poly_list.append((0, 0))
        self.polynomial = tuple(simplified_poly_list)

    def __str__(self):
        if len(self.polynomial) == 0:
            return ''
        if len(self.polynomial) == 1 and self.polynomial[0] == (0, 0):
            return '0'
        poly_string = ''
        for index, (coef, exp) in enumerate(self.polynomial):
            temp_string = ''
            if index == 0:
                temp_string += '-' if coef < 0 else ''
            else:
                temp_string += ' - ' if coef < 0 else ' + '
            abs_coef = abs(coef)
            if abs_coef != 1 or exp == 0:
                temp_string += f'{abs_coef}'
            if exp != 0:
                temp_string += 'x'
                if exp != 1:
                    temp_string += f'^{exp}'
            poly_string += temp_string
        return poly_string if poly_string else '0'


############################################################
# Section 7: Python Packages
############################################################


def sort_array(list_of_matrices):
    flattened_array = np.concatenate([
        np.array(matrix).ravel() for matrix in list_of_matrices
    ])
    return np.sort(flattened_array)[::-1]


def POS_tag(sentence):
    # Commented for submission.
    # nltk.download('stopwords')
    # nltk.download('punkt')
    # nltk.download('averaged_perceptron_tagger')
    sentence = sentence.lower()
    tokens = nltk.word_tokenize(sentence)
    stop_words = nltk.corpus.stopwords.words('english')
    tokens = [
        token for token in tokens if token not in stop_words
        and token not in string.punctuation
    ]
    return nltk.pos_tag(tokens)


############################################################
# Section 8: Feedback
############################################################


# Just an approximation is fine.
feedback_question_1 = """
I spent approximately 6.5 hours on this homework assignment,
not counting time spent on lectures and textbook readings.
"""

feedback_question_2 = """
I found the generators and Polynomial class the most challenging.
"""

feedback_question_3 = """
I enjoyed the string manipulation and slicing exercises. In
terms of feedback, I would have made the first homework
assignment less time-consuming, given the amount of lectures
and textbook readings required for this week. I would have
been very frustrated if I didn't work with Python daily or
start the assignment before the course began.
"""
