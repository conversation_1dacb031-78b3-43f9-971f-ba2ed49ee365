import time

def concatenate1(strings):
    result= ""
    for s in strings:
        result += s
    return result


def concatenate2(strings):
    return "".join(strings)



    

strings = []
for i in range(1000000000):
    strings.append("string" + str(i))
    
print('*'*100)

start_time1 = time.time()
concatenate1(strings)
end_time1 = time.time()
print("Time taken by concatenate1: ", end_time1 - start_time1)

start_time2 = time.time()
concatenate2(strings)
end_time2 = time.time()
print("Time taken by concatenate2: ", end_time2 - start_time2)

print("(end_time2 - start_time2) < (end_time1 - start_time1)", (end_time2 - start_time2) < (end_time1 - start_time1))
print('*'*100)
