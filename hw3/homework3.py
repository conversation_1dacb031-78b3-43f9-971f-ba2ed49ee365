############################################################
# Imports
############################################################

import collections
import itertools
import queue
import random

############################################################
# CIS 521: Homework 3
############################################################

student_name = "Valencia Richards"

############################################################
# Section 1: Tile Puzzle
############################################################


def create_tile_puzzle(rows, cols):
    board = list()
    start = 1
    for row in range(rows):
        board.append(list())
        for col in range(cols):
            board[row].append(start)
            start += 1
    board[-1][-1] = 0
    return TilePuzzle(board)


class TilePuzzle(object):
    """
    A natural representation for this puzzle is a
    two-dimensional list of integer values between 0 and
    r·c-1 (inclusive), (e.g. 0 - 8 for a 3 x 3 board)
    where r and c are the number of rows
    and columns in the board, respectively. In this problem,
    we will adhere to the convention that the 0-tile
    represents the empty space.
    """

    # Required
    def __init__(self, board):
        self.board = [list(row) for row in board]
        self.row_count = len(board)
        self.col_count = len(board[0])
        self.empty_tile_row_index = None
        self.empty_tile_column_index = None
        empty_tile_found = False
        for row_index, row in enumerate(board):
            if empty_tile_found:
                break
            for column_index, tile_number in enumerate(row):
                if tile_number == 0:
                    self.empty_tile_row_index = row_index
                    self.empty_tile_column_index = column_index
                    empty_tile_found = True
                    break

    def get_board(self):
        return self.board

    def perform_move(self, direction):
        pass

    def scramble(self, num_moves):
        pass

    def is_solved(self):
        pass

    def copy(self):
        copied_board = [list(row) for row in self.board]
        return TilePuzzle(copied_board)

    def successors(self):
        pass

    # Required
    def find_solutions_iddfs(self):
        # Iterative deepening search?
        pass

    # Required
    def find_solution_a_star(self):
        # A* algorithm from Red Blob Games
        # https://www.redblobgames.com/pathfinding/
        # a-star/introduction.html
        # Ed Discussion post:
        # https://edstem.org/us/courses/78274/discussion/6718380

        # frontier = PriorityQueue()
        # frontier.put(start, 0)
        # came_from = dict()
        # cost_so_far = dict()
        # came_from[start] = None
        # cost_so_far[start] = 0

        # while not frontier.empty():
        # current = frontier.get()

        # if current == goal:
        #     break

        # for next in graph.neighbors(current):
        #     new_cost = cost_so_far[current] + graph.cost(current, next)
        #     if next not in cost_so_far or new_cost < cost_so_far[next]:
        #         cost_so_far[next] = new_cost
        #         priority = new_cost + heuristic(goal, next)
        #         frontier.put(next, priority)
        #         came_from[next] = current
        pass

############################################################
# Section 2: Grid Navigation
############################################################


def find_path(start, goal, scene):
    pass

############################################################
# Section 3: Linear Disk Movement, Revisited
############################################################


def solve_distinct_disks(length, n):
    pass

############################################################
# Section 4: Feedback
############################################################


# Just an approximation is fine.
feedback_question_1 = """
I spent about 4 hours on this assignment.
"""

feedback_question_2 = """
The most challenging part was understanding the logic for
linear disk movement.
"""

feedback_question_3 = """
The theory of informed search was very interesting.
"""
