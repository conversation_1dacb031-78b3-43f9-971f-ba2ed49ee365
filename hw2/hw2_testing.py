from homework2 import *

# Section 1: N-Queens
# print('*'*100)
# print(num_placements_one_per_row(1))
# print(num_placements_one_per_row(2))
# print(num_placements_one_per_row(3))
# print(num_placements_one_per_row(4))
# print(num_placements_one_per_row(5))
# print(num_placements_one_per_row(6))
# print('*'*100)
# print(n_queens_valid([0, 0]))
# print(n_queens_valid([0, 2]))
# print(n_queens_valid([0, 1]))
# print(n_queens_valid([0, 3, 1]))
# print('*'*100)
# print(n_queens_solutions(6))
# print(len(n_queens_solutions(8)))
# print('*'*100)

# Section 2: Lights Out
# print('*'*100)
# b= [[True, False], [False, True]]
# p = LightsOutPuzzle(b)
# print(p.get_board())
# b= [[True, True], [True, True]]
# p = LightsOutPuzzle(b)
# print(p.get_board())
# print('*'*100)
# p = create_puzzle(2, 2)
# print(p.get_board())
# p = create_puzzle(2, 3)
# print(p.get_board())
# print('*'*100)
# p = create_puzzle(3, 3)
# p.perform_move(1, 1)
# print(p.get_board())
# p = create_puzzle(3, 3)
# p.perform_move(0, 0)
# print(p.get_board())
# print('*'*100)
# b = [[True, False], [False, True]]
# p = LightsOutPuzzle(b)
# print(p.is_solved())
# b = [[False, False], [False, False]]
# p = LightsOutPuzzle(b)
# print(p.is_solved())
# print('*'*100)
# p= create_puzzle(3, 3)
# p2 = p.copy()
# print(p.get_board() == p2.get_board())
# p= create_puzzle(3, 3)
# p2 = p.copy()
# p.perform_move(1, 1)
# print(p.get_board() == p2.get_board())
# print('*'*100)
# p= create_puzzle(2, 2)
# for move, new_p in p.successors():
#     print(move, new_p.get_board())
# for i in range(2, 6):
#     p= create_puzzle(i, i + 1)
#     print(len(list(p.successors())))
# print('*'*100)
# p= create_puzzle(2, 3)
# for row in range(2):
#     for col in range(3):
#         p.perform_move(row, col)
# print(p.find_solution())
# b = [[False, False, False],
# [False, False, False]]
# b[0][0] = True
# p = LightsOutPuzzle(b)
# print(p.find_solution() is None)
# print('*'*100)

# Section 3: Linear Disk Movement
# print('*'*100)
# print(solve_identical_disks(4, 2))
# print(solve_identical_disks(5, 2))
# print(solve_identical_disks(4, 3))
# print(solve_identical_disks(5, 3))
# print('*'*100)
# print(solve_distinct_disks(4, 2))
# print(solve_distinct_disks(5, 2))
# print(solve_distinct_disks(4, 3))
# print(solve_distinct_disks(5, 3))
# print('*'*100)

