############################################################
# Imports
############################################################

import collections
import itertools
import math
import random


# ############################################################
# CIS 521: Homework 2
############################################################

student_name = "<PERSON> Richards"

############################################################
# Section 1: N-Queens
############################################################


def num_placements_all(n):
    """
    Returns the number of all possible ways to place n queens
    on an n x n board.
    """
    # The number of ways to chose n positions from n^2 positions
    # (n^2 choose n).
    # math.comb(n, k)
    return math.comb(n**2, n)


def num_placements_one_per_row(n):
    """
    Returns the number of all possible ways to place n queens
    on an n x n board such that each row contains exactly one
    queen.
    """
    # On each row, you can place a queen in n ways. And for
    # each of those n ways, the queen in the next row can be
    # placed in n ways, etc. Since placing queens in different
    # rows are independent choices, we multiply the number of
    # options for each choice, giving us n^n total arrangements.
    return n**n


def n_queens_valid(board):
    # Recall that in chess, a queen can attack any piece that
    # lies in the same row, column, or diagonal as itself.

    # Check columns
    for column in range(len(board)):
        if board.count(column) > 1:
            return False
    # Check diagonals
    for row1 in range(len(board)):
        for row2 in range(row1 + 1, len(board)):
            # Queens are on the same row if the difference in
            # rows and the difference in columns are the same.
            if abs(row1 - row2) == abs(board[row1] - board[row2]):
                return False
    return True


def n_queens_helper(n, board):
    """
    You may find it helpful to define a helper function
    n_queens_helper(n, board) that yields all valid placements
    which extend the partial solution denoted by board.
    """
    # If the board is full, we're done
    if len(board) == n:
        yield board
    # Otherwise, try adding a queen to each column
    for column in range(n):
        # If the board is valid with the added queen,
        # recursively call this function to add more queens
        # until the board is full.
        # This (progressively adding queens) is what makes this DFS.
        # Reference post 70 on Ed Discussion:
        # https://edstem.org/us/courses/78274/discussion/6708052
        if n_queens_valid(board + [column]):
            # 'yield from' is used when yielding another
            # generator or iterable.
            yield from n_queens_helper(n, board + [column])


def n_queens_solutions(n):
    """
    Returns a list of all valid placements of n queens on an
    n x n board, using the representation discussed above. Your
    solution should be implemented as a depth-first search, where
    queens are successively placed in empty rows until all rows
    have been filled.
    """
    solutions = list()
    for num in range(n):
        # Use the helper function to find all valid solutions
        for solution in n_queens_helper(n, list()):
            # Lists can't be added to sets since they're not
            # hashable, so this is a workaround to avoid
            #  duplication.
            if solution not in solutions:
                solutions.append(solution)
    return solutions


############################################################
# Section 2: Lights Out
############################################################


class LightsOutPuzzle(object):

    def __init__(self, board):
        self.board = list(board)
        self.row_count = len(board)
        self.col_count = len(board[0])

    def get_board(self):
        return self.board

    def perform_move(self, row, col):
        self.board[row][col] = not self.board[row][col]
        if row > 0:
            self.board[row - 1][col] = not self.board[row - 1][col]
        if row < self.row_count - 1:
            self.board[row + 1][col] = not self.board[row + 1][col]
        if col > 0:
            self.board[row][col - 1] = not self.board[row][col - 1]
        if col < self.col_count - 1:
            self.board[row][col + 1] = not self.board[row][col + 1]

    def scramble(self):
        for row in range(self.row_count):
            for col in range(self.col_count):
                if random.random() < 0.5:
                    self.perform_move(row, col)

    def is_solved(self):
        for row in range(self.row_count):
            for col in range(self.col_count):
                if self.board[row][col] is True:
                    return False
        return True

    def copy(self):
        new_board = list()
        for row in range(self.row_count):
            new_board.append(list())
            for col in range(self.col_count):
                new_board[row].append(self.board[row][col])
        return LightsOutPuzzle(new_board)

    def successors(self):
        for row in range(self.row_count):
            for col in range(self.col_count):
                move = (row, col)
                copied_puzzle = self.copy()
                copied_puzzle.perform_move(*move)
                yield (move, copied_puzzle)

    def find_solution(self):
        # Breadth-first search (BFS) graph search algorithm.

        # Initialize the frontier using the initial state of
        # the problem (as tuples as per HW isntructions)
        initial_state = tuple(tuple(row) for row in self.board)
        frontier = collections.deque([
            (initial_state, [])
        ])
        # Initialize the explored set to be empty
        explored_set = set()
        while True:
            # If the frontier is empty then return failure
            if not frontier:
                return None
            # Choose a leaf node and remove it from the frontier
            # popleft removes from the left side of the deque
            board, moves = frontier.popleft()
            puzzle = LightsOutPuzzle(board)
            # If the node contains a goal state then return the
            # corresponding solution
            if puzzle.is_solved():
                return moves
            else:
                # Add node to the explored set
                explored_set.add(tuple(board))
                # Expand the chosen node, adding the resulting nodes
                # to the frontier only if not in the frontier or
                # explored set
                for move, successor_puzzle in puzzle.successors():
                    successor_board = tuple(
                        tuple(row) for row in successor_puzzle.get_board()
                    )
                    if successor_board not in explored_set:
                        new_moves = moves + [move]
                        if (successor_board, new_moves) not in frontier:
                            frontier.append((successor_board, new_moves))


def create_puzzle(row_count, col_count):
    return LightsOutPuzzle([
        [False for col in range(col_count)]
        for row in range(row_count)
    ])


############################################################
# Section 3: Linear Disk Movement
############################################################


def solve_identical_disks(length, n):
    # BFS
    # Initialize the puzzle
    puzzle = [True for disk in range(n)]
    for empty_cell in range(n, length):
        puzzle.append(False)
    moves = set()

    # # Initialize the frontier using the initial state of
    # # the problem
    # initial_state = tuple(tuple(row) for row in self.board)
    # frontier = collections.deque([
    #     (initial_state, [])
    # ])
    # # Initialize the explored set to be empty
    # explored_set = set()
    # while True:
    #     # If the frontier is empty then return failure
    #     if not frontier:
    #         return None
    #     # Choose a leaf node and remove it from the frontier
    #     # popleft removes from the left side of the deque
    #     board, moves = frontier.popleft()
    #     puzzle = LightsOutPuzzle(board)
    #     # If the node contains a goal state then return the
    #     # corresponding solution
    #     if puzzle.is_solved():
    #         return moves
    #     else:
    #         # Add node to the explored set
    #         explored_set.add(tuple(board))
    #         # Expand the chosen node, adding the resulting nodes
    #         # to the frontier only if not in the frontier or
    #         # explored set
    #         for move, successor_puzzle in puzzle.successors():
    #             successor_board = tuple(
    #                 tuple(row) for row in successor_puzzle.get_board()
    #             )
    #             if successor_board not in explored_set:
    #                 new_moves = moves + [move]
    #                 if (successor_board, new_moves) not in frontier:
    #                     frontier.append((successor_board, new_moves))

    return list(moves)


def solve_distinct_disks(length, n):
    # TODO
    # BFS
    # Initialize the puzzle
    # Number the disks so we chan check that they're sorted.
    puzzle = [disk for disk in range(1, n + 1)]
    for empty_cell in range(n, length):
        puzzle.append(0)
    moves = set()
    return list(moves)


############################################################
# Section 4: Feedback
############################################################


# Just an approximation is fine.
feedback_question_1 = """
I spent about 9 hours on this assignment.
"""

feedback_question_2 = """
The most challenging part was understanding the logic for
linear disk movement.
"""

feedback_question_3 = """
I enjoyed implementing the LightsOutPuzzle class.
"""
