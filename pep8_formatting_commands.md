From [Ed Discussion post 34](https://edstem.org/us/courses/78274/discussion/6684555)

Hi,

You may use pycodestyle and autopep8 in command line.

To install, run:

pip3 install pycodestyle
pip3 install autopep8
To run them, you would normally do something like the following:

pycodestyle homework1.py # initial style check
autopep8 -ia homework1.py #automatic code formatting
pycodestyle homework1.py # verify changes
Note:

The -ia flags used with autopep8 serve specific purposes: 

-i (in-place): Modifies the file directly.

-a (aggressive): Applies more extensive modifications to more strictly adhere to PEP 8.

Note: autopep8 does not correct "E501 line too long" errors due to the lack of a safe, automated solution. You'll need to manually adjust line lengths as necessary.

Hope this helps

<PERSON>